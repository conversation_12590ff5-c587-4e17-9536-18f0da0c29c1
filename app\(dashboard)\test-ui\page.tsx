"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Notification, NotificationExamples } from "@/components/ui/notification"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import { TriangleAlertIcon, CheckCircleIcon, InfoIcon, AlertCircleIcon } from "lucide-react"

export default function TestUIPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">Origin UI Component Testing</h1>
            <p className="text-muted-foreground">
              Testing the improved Origin UI components with proper shadow utilities and dark/light mode
            </p>
          </div>
          <ThemeToggle />
        </div>

      {/* Button Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Button Components</CardTitle>
          <CardDescription>
            Testing different button variants with improved shadows
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="default">Default Button</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="destructive">Destructive</Button>
            <Button variant="success">Success</Button>
            <Button variant="warning">Warning</Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button size="sm">Small</Button>
            <Button size="default">Default</Button>
            <Button size="lg">Large</Button>
            <Button size="icon">
              <CheckCircleIcon className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Input Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Input Components</CardTitle>
          <CardDescription>
            Testing input fields with improved focus states
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input placeholder="Default input" />
            <Input placeholder="Search..." type="search" />
            <Input placeholder="Email" type="email" />
            <Input placeholder="Password" type="password" />
          </div>
        </CardContent>
      </Card>

      {/* Alert Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Alert Components</CardTitle>
          <CardDescription>
            Testing alert components with improved styling
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <InfoIcon className="h-4 w-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              This is a default alert with information styling.
            </AlertDescription>
          </Alert>
          
          <Alert variant="destructive">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              This is a destructive alert indicating an error condition.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Notification Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Components</CardTitle>
          <CardDescription>
            Testing the new notification component with Origin UI styling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NotificationExamples />
        </CardContent>
      </Card>

      {/* Card Examples */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Card Example 1</CardTitle>
            <CardDescription>
              A simple card with shadow-sm
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card should now have proper shadows applied.
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Card Example 2</CardTitle>
            <CardDescription>
              A card with shadow-md
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card has a more prominent shadow.
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Card Example 3</CardTitle>
            <CardDescription>
              A card with shadow-lg
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card has the largest shadow.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Badge Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Badge Components</CardTitle>
          <CardDescription>
            Testing badge components with improved styling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Badge>Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge variant="outline">Outline</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Shadow Testing */}
      <Card>
        <CardHeader>
          <CardTitle>Shadow Utility Testing</CardTitle>
          <CardDescription>
            Testing all shadow utilities to ensure they work properly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-white rounded-md shadow-xs border">
              <p className="text-sm font-medium">shadow-xs</p>
            </div>
            <div className="p-4 bg-white rounded-md shadow-sm border">
              <p className="text-sm font-medium">shadow-sm</p>
            </div>
            <div className="p-4 bg-white rounded-md shadow-md border">
              <p className="text-sm font-medium">shadow-md</p>
            </div>
            <div className="p-4 bg-white rounded-md shadow-lg border">
              <p className="text-sm font-medium">shadow-lg</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
