"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Notification, NotificationExamples } from "@/components/ui/notification"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import { useToast } from "@/hooks/use-toast"
import { TriangleAlertIcon, CheckCircleIcon, InfoIcon, AlertCircleIcon, Sparkles, Palette, Zap } from "lucide-react"

export default function TestUIPage() {
  const { toast } = useToast()

  const showToast = (variant: "default" | "destructive" | "success" | "warning" | "info") => {
    const toastConfig = {
      default: { title: "Default Toast", description: "This is a default notification message." },
      destructive: { title: "Error Occurred", description: "Something went wrong. Please try again." },
      success: { title: "Success!", description: "Your action was completed successfully." },
      warning: { title: "Warning", description: "Please review your input before proceeding." },
      info: { title: "Information", description: "Here's some helpful information for you." },
    }

    toast({
      variant,
      ...toastConfig[variant],
    })
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold flex items-center">
              <Sparkles className="h-8 w-8 mr-3 text-primary" />
              Professional CRM UI Showcase
            </h1>
            <p className="text-muted-foreground text-lg">
              Experience the enhanced Origin UI components with professional styling, shadows, and seamless dark/light mode support
            </p>
          </div>
          <ThemeToggle />
        </div>

        {/* Professional Toast Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="h-5 w-5 mr-2 text-primary" />
              Professional Toast Notifications
            </CardTitle>
            <CardDescription>
              Test the enhanced notification system with different variants and professional styling
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button onClick={() => showToast("default")} variant="outline">
                Default Toast
              </Button>
              <Button onClick={() => showToast("success")} variant="default">
                Success Toast
              </Button>
              <Button onClick={() => showToast("warning")} variant="secondary">
                Warning Toast
              </Button>
              <Button onClick={() => showToast("info")} variant="outline">
                Info Toast
              </Button>
              <Button onClick={() => showToast("destructive")} variant="destructive">
                Error Toast
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Professional Stats Showcase */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-5 w-5 mr-2 text-primary" />
              Enhanced Statistics Display
            </CardTitle>
            <CardDescription>
              Professional stats cards with improved shadows, colors, and responsive design
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 rounded-lg border border-border bg-card shadow-sm overflow-hidden">
              <div className="relative p-6 group hover:bg-accent/50 transition-colors duration-200 border-r border-border last:border-r-0">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                      Total Students
                    </p>
                    <div className="space-y-1">
                      <p className="text-3xl font-bold tracking-tight">1,247</p>
                      <div className="flex items-center space-x-1 text-sm">
                        <span className="font-medium flex items-center text-green-600 dark:text-green-400">
                          ↗ +12.5%
                        </span>
                        <span className="text-muted-foreground">vs last month</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
                    <CheckCircleIcon className="h-5 w-5" />
                  </div>
                </div>
              </div>
              <div className="relative p-6 group hover:bg-accent/50 transition-colors duration-200 border-r border-border last:border-r-0">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                      Monthly Revenue
                    </p>
                    <div className="space-y-1">
                      <p className="text-3xl font-bold tracking-tight">$24,580</p>
                      <div className="flex items-center space-x-1 text-sm">
                        <span className="font-medium flex items-center text-green-600 dark:text-green-400">
                          ↗ +8.2%
                        </span>
                        <span className="text-muted-foreground">vs last month</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
                    <InfoIcon className="h-5 w-5" />
                  </div>
                </div>
              </div>
              <div className="relative p-6 group hover:bg-accent/50 transition-colors duration-200 border-r border-border last:border-r-0">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                      Active Courses
                    </p>
                    <div className="space-y-1">
                      <p className="text-3xl font-bold tracking-tight">42</p>
                      <div className="flex items-center space-x-1 text-sm">
                        <span className="font-medium flex items-center text-green-600 dark:text-green-400">
                          ↗ +3
                        </span>
                        <span className="text-muted-foreground">vs last month</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
                    <TriangleAlertIcon className="h-5 w-5" />
                  </div>
                </div>
              </div>
              <div className="relative p-6 group hover:bg-accent/50 transition-colors duration-200">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                      Completion Rate
                    </p>
                    <div className="space-y-1">
                      <p className="text-3xl font-bold tracking-tight">94.2%</p>
                      <div className="flex items-center space-x-1 text-sm">
                        <span className="font-medium flex items-center text-green-600 dark:text-green-400">
                          ↗ +2.1%
                        </span>
                        <span className="text-muted-foreground">vs last month</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
                    <AlertCircleIcon className="h-5 w-5" />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

      {/* Button Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Button Components</CardTitle>
          <CardDescription>
            Testing different button variants with improved shadows
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="default">Default Button</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="destructive">Destructive</Button>
            <Button variant="success">Success</Button>
            <Button variant="warning">Warning</Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button size="sm">Small</Button>
            <Button size="default">Default</Button>
            <Button size="lg">Large</Button>
            <Button size="icon">
              <CheckCircleIcon className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Input Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Input Components</CardTitle>
          <CardDescription>
            Testing input fields with improved focus states
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input placeholder="Default input" />
            <Input placeholder="Search..." type="search" />
            <Input placeholder="Email" type="email" />
            <Input placeholder="Password" type="password" />
          </div>
        </CardContent>
      </Card>

      {/* Alert Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Alert Components</CardTitle>
          <CardDescription>
            Testing alert components with improved styling
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <InfoIcon className="h-4 w-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              This is a default alert with information styling.
            </AlertDescription>
          </Alert>
          
          <Alert variant="destructive">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              This is a destructive alert indicating an error condition.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Notification Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Components</CardTitle>
          <CardDescription>
            Testing the new notification component with Origin UI styling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NotificationExamples />
        </CardContent>
      </Card>

      {/* Card Examples */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Card Example 1</CardTitle>
            <CardDescription>
              A simple card with shadow-sm
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card should now have proper shadows applied.
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Card Example 2</CardTitle>
            <CardDescription>
              A card with shadow-md
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card has a more prominent shadow.
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Card Example 3</CardTitle>
            <CardDescription>
              A card with shadow-lg
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This card has the largest shadow.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Badge Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Badge Components</CardTitle>
          <CardDescription>
            Testing badge components with improved styling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Badge>Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge variant="outline">Outline</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Shadow Testing */}
      <Card>
        <CardHeader>
          <CardTitle>Shadow Utility Testing</CardTitle>
          <CardDescription>
            Testing all shadow utilities to ensure they work properly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-white rounded-md shadow-xs border">
              <p className="text-sm font-medium">shadow-xs</p>
            </div>
            <div className="p-4 bg-white rounded-md shadow-sm border">
              <p className="text-sm font-medium">shadow-sm</p>
            </div>
            <div className="p-4 bg-white rounded-md shadow-md border">
              <p className="text-sm font-medium">shadow-md</p>
            </div>
            <div className="p-4 bg-white rounded-md shadow-lg border">
              <p className="text-sm font-medium">shadow-lg</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
