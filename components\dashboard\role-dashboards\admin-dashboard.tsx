'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { StatsGrid } from '@/components/stats-grid'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import {
  Users,
  UserPlus,
  GraduationCap,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  CheckCircle,
  Clock,
  ArrowUpRight,
  BarChart3,
  BookOpen,
  Loader2,
  Shield,
  Activity,
  PieChart
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { useDashboardStore } from '@/lib/stores/dashboard-store'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

interface AdminDashboardStats {
  totalStudents: { count: number; growth: number }
  newLeads: { count: number; growth: number }
  activeGroups: { count: number }
  monthlyRevenue: { amount: number; growth: number }
  totalUsers: { count: number }
  systemHealth: { status: string; uptime: string }
  recentLeads: Array<{ name: string; course: string; status: string; time: string }>
  upcomingClasses: Array<{ group: string; teacher: string; time: string; room: string }>
  recentActivity: Array<{ action: string; user: string; time: string }>
}

export default function AdminDashboard() {
  const { currentBranch } = useBranch()
  const { refreshData } = useDashboardStore()
  const [stats, setStats] = useState<AdminDashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAdminStats()
    refreshData()
  }, [])

  const fetchAdminStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/admin-stats')
      if (!response.ok) {
        throw new Error('Failed to fetch admin dashboard stats')
      }
      const data = await response.json()
      setStats(data)
      setError(null)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching admin dashboard stats:', error)
      }
      setError('Failed to load admin dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('UZS', 'UZS')
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchAdminStats}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">
            Admin Dashboard
          </h1>
          <p className="text-sm text-muted-foreground">
            Complete system overview and administrative controls for {currentBranch?.name || 'your center'}.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              fetchAdminStats()
              refreshData()
            }}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
          <Link href="/dashboard/analytics">
            <Button size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
          </Link>
        </div>
      </div>

      {/* Admin Stats Grid */}
      <StatsGrid
        stats={[
          {
            title: "Total Revenue",
            value: formatCurrency(stats?.monthlyRevenue.amount || 0),
            change: {
              value: `${(stats?.monthlyRevenue.growth ?? 0) >= 0 ? '+' : ''}${stats?.monthlyRevenue.growth || 0}%`,
              trend: (stats?.monthlyRevenue.growth ?? 0) >= 0 ? "up" : "down"
            },
            icon: <DollarSign className="h-5 w-5" />
          },
          {
            title: "Total Users",
            value: formatNumber(stats?.totalUsers.count || 0),
            change: {
              value: "System users",
              trend: "up"
            },
            icon: <Users className="h-5 w-5" />
          },
          {
            title: "Total Students",
            value: formatNumber(stats?.totalStudents.count || 0),
            change: {
              value: `${(stats?.totalStudents.growth ?? 0) >= 0 ? '+' : ''}${stats?.totalStudents.growth || 0}%`,
              trend: (stats?.totalStudents.growth ?? 0) >= 0 ? "up" : "down"
            },
            icon: <GraduationCap className="h-5 w-5" />
          },
          {
            title: "System Health",
            value: "Healthy",
            change: {
              value: "All operational",
              trend: "up"
            },
            icon: <Activity className="h-5 w-5" />
          }
        ]}
      />

      {/* Admin Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Administrative shortcuts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Link href="/dashboard/users">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Users
                </Button>
              </Link>
              <Link href="/dashboard/analytics">
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </Button>
              </Link>
              <Link href="/dashboard/admin/activity-logs">
                <Button variant="outline" className="w-full justify-start">
                  <Activity className="h-4 w-4 mr-2" />
                  Activity Logs
                </Button>
              </Link>
              <Link href="/dashboard/admin/kpis">
                <Button variant="outline" className="w-full justify-start">
                  <PieChart className="h-4 w-4 mr-2" />
                  KPI Dashboard
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Leads</CardTitle>
                <CardDescription>Latest inquiries</CardDescription>
              </div>
              <Link href="/dashboard/leads">
                <Button variant="ghost" size="sm">
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.recentLeads && stats.recentLeads.length > 0 ? (
                stats.recentLeads.slice(0, 3).map((lead, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors duration-200">
                    <div>
                      <p className="font-medium">{lead.name}</p>
                      <p className="text-sm text-muted-foreground">{lead.course}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground mb-1">{lead.time}</p>
                      <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                        {lead.status}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <UserPlus className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No recent leads</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* System Activity Feed */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>System Activity</CardTitle>
                <CardDescription>Recent system events</CardDescription>
              </div>
              <Link href="/dashboard/admin/activity-logs">
                <Button variant="ghost" size="sm">
                  <Clock className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <ActivityFeed
              limit={6}
              showHeader={false}
              showRefresh={false}
              showViewAll={false}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
