<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Origin UI Component Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    boxShadow: {
                        'xs': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
                        'sm': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                        'md': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                        'lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
                        'xl': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
                        '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
                    }
                }
            }
        }
    </script>
    <style>
        .btn {
            @apply inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-blue-500;
        }
        .btn-default {
            @apply bg-blue-600 border border-blue-500 text-white shadow-sm hover:bg-blue-700;
        }
        .btn-sm {
            @apply h-8 rounded-md px-3 text-xs;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
        <div class="space-y-2">
            <h1 class="text-3xl font-bold text-gray-900">Origin UI Component Testing</h1>
            <p class="text-gray-600">
                Testing the improved Origin UI components with proper shadow utilities
            </p>
        </div>

        <!-- Original Example from Origin UI -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Original Origin UI Example</h2>
            <div class="bg-white z-50 max-w-[400px] rounded-md border p-4 shadow-lg">
                <div class="flex gap-2">
                    <div class="flex grow gap-3">
                        <svg class="mt-0.5 shrink-0 text-amber-500" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                            <path d="M12 9v4"/>
                            <path d="m12 17 .01 0"/>
                        </svg>
                        <div class="flex grow flex-col gap-3">
                            <div class="space-y-1">
                                <p class="text-sm font-medium">
                                    Something requires your action!
                                </p>
                                <p class="text-gray-600 text-sm">
                                    It conveys that a specific action is needed to resolve or address a situation.
                                </p>
                            </div>
                            <div>
                                <button class="btn btn-default btn-sm">Learn more</button>
                            </div>
                        </div>
                    </div>
                    <button class="group -my-1.5 -me-2 w-8 h-8 shrink-0 p-0 hover:bg-transparent rounded-md flex items-center justify-center" aria-label="Close notification">
                        <svg width="16" height="16" class="opacity-60 transition-opacity group-hover:opacity-100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"/>
                            <path d="m6 6 12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Shadow Testing -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Shadow Utility Testing</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="p-4 bg-white rounded-md shadow-xs border">
                    <p class="text-sm font-medium">shadow-xs</p>
                    <p class="text-xs text-gray-500">Very subtle</p>
                </div>
                <div class="p-4 bg-white rounded-md shadow-sm border">
                    <p class="text-sm font-medium">shadow-sm</p>
                    <p class="text-xs text-gray-500">Small shadow</p>
                </div>
                <div class="p-4 bg-white rounded-md shadow-md border">
                    <p class="text-sm font-medium">shadow-md</p>
                    <p class="text-xs text-gray-500">Medium shadow</p>
                </div>
                <div class="p-4 bg-white rounded-md shadow-lg border">
                    <p class="text-sm font-medium">shadow-lg</p>
                    <p class="text-xs text-gray-500">Large shadow</p>
                </div>
            </div>
        </div>

        <!-- Button Testing -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Button Testing</h2>
            <div class="space-y-4">
                <div class="flex flex-wrap gap-2">
                    <button class="btn btn-default">Default Button</button>
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-gray-500 border border-gray-300 bg-white shadow-xs hover:bg-gray-50 h-9 px-4 py-2">Outline</button>
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-gray-500 bg-gray-100 text-gray-900 shadow-xs hover:bg-gray-200 h-9 px-4 py-2">Secondary</button>
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-red-500 bg-red-600 text-white shadow-xs hover:bg-red-700 h-9 px-4 py-2">Destructive</button>
                </div>
            </div>
        </div>

        <!-- Input Testing -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Input Testing</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input type="text" placeholder="Default input" class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
                <input type="search" placeholder="Search..." class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
                <input type="email" placeholder="Email" class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
                <input type="password" placeholder="Password" class="flex h-9 w-full min-w-0 rounded-md border border-gray-300 bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none placeholder:text-gray-500 focus-visible:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500/20">
            </div>
        </div>

        <!-- Comparison -->
        <div class="bg-white rounded-lg border p-6 shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Before vs After Comparison</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium mb-3 text-red-600">Before (No shadow-xs)</h3>
                    <div class="bg-white max-w-[400px] rounded-md border p-4">
                        <div class="flex gap-2">
                            <div class="flex grow gap-3">
                                <svg class="mt-0.5 shrink-0 text-amber-500" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                                    <path d="M12 9v4"/>
                                    <path d="m12 17 .01 0"/>
                                </svg>
                                <div class="flex grow flex-col gap-3">
                                    <div class="space-y-1">
                                        <p class="text-sm font-medium">Something requires your action!</p>
                                        <p class="text-gray-600 text-sm">Flat appearance without shadows</p>
                                    </div>
                                    <div>
                                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium bg-blue-600 text-white h-8 px-3">Learn more</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-medium mb-3 text-green-600">After (With shadow-xs & shadow-lg)</h3>
                    <div class="bg-white max-w-[400px] rounded-md border p-4 shadow-lg">
                        <div class="flex gap-2">
                            <div class="flex grow gap-3">
                                <svg class="mt-0.5 shrink-0 text-amber-500" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                                    <path d="M12 9v4"/>
                                    <path d="m12 17 .01 0"/>
                                </svg>
                                <div class="flex grow flex-col gap-3">
                                    <div class="space-y-1">
                                        <p class="text-sm font-medium">Something requires your action!</p>
                                        <p class="text-gray-600 text-sm">Professional appearance with proper shadows</p>
                                    </div>
                                    <div>
                                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium bg-blue-600 text-white shadow-xs hover:bg-blue-700 h-8 px-3">Learn more</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
