'use client'

import { useSession } from 'next-auth/react'
import { Loader2 } from 'lucide-react'

// Import role-specific dashboard components
import AdminDashboard from '@/components/dashboard/role-dashboards/admin-dashboard'
import ManagerDashboard from '@/components/dashboard/role-dashboards/manager-dashboard'
import ReceptionDashboard from '@/components/dashboard/role-dashboards/reception-dashboard'
import CashierDashboard from '@/components/dashboard/role-dashboards/cashier-dashboard'

export default function DashboardPage() {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!session?.user?.role) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center space-y-4">
          <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-6">
            <p className="text-destructive font-medium">Unable to determine user role</p>
            <p className="text-sm text-muted-foreground mt-2">Please contact your administrator</p>
          </div>
        </div>
      </div>
    )
  }

  // Route to appropriate dashboard based on user role
  const userRole = session.user.role

  switch (userRole) {
    case 'ADMIN':
      return <AdminDashboard />
    case 'MANAGER':
      return <ManagerDashboard />
    case 'RECEPTION':
      return <ReceptionDashboard />
    case 'CASHIER':
      return <CashierDashboard />
    default:
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-red-600">Unknown user role: {userRole}</p>
          </div>
        </div>
      )
  }
}



